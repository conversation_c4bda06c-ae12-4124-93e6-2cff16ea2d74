import React, { useState, useCallback, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import ReactMarkdown from "react-markdown";
import { Prism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter";
import { tomorrow, prism } from "react-syntax-highlighter/dist/esm/styles/prism";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import {
  FiSave,
  FiEye,
  FiEdit3,
  FiImage,
  FiTable,
  FiCode,
  FiLink,
  FiList,
  FiClock,
  FiTag,
  FiBold,
  FiItalic,
  FiMessageSquare,
  FiMinus,
  FiCheck,
  FiUpload,
  FiDownload,
  FiRefreshCw,
  FiHash,
  FiMaximize2,
  FiMinimize2,
  FiCopy,
  FiZap,
} from "react-icons/fi";
import PageAnimation from "../../components/common/PageAnimation/PageAnimation";
import Tooltip from "../../components/common/Tooltip/Tooltip";
import "./BlogEditorPage.css";

interface BlogPost {
  id: string;
  title: string;
  content: string;
  tags: string[];
  createdAt: Date;
  modificationHistory: ModificationEntry[];
}

interface ModificationEntry {
  id: string;
  timestamp: Date;
  description: string;
  author: string;
}

const BlogEditorPage: React.FC = () => {
  const [blogPost, setBlogPost] = useState<BlogPost>({
    id: "1",
    title: "",
    content:
      '# Welcome to Blog Editor\n\nStart writing your amazing content here...\n\n## Features\n\n- **Real-time preview** with syntax highlighting\n- **Rich toolbar** with quick insert tools\n- **Tag management** system\n- **Auto-save** functionality\n- **Modification history** tracking\n\n### Code Example\n\n```javascript\nconst greeting = "Hello, World!";\nconsole.log(greeting);\n```\n\n### Table Example\n\n| Feature | Status | Priority |\n|---------|--------|---------|\n| Editor | ✅ Done | High |\n| Preview | ✅ Done | High |\n| Tags | ✅ Done | Medium |\n\n> This is a blockquote example. Perfect for highlighting important information!\n\n---\n\nHappy writing! 🚀',
    tags: ["markdown", "editor", "blog"],
    createdAt: new Date(),
    modificationHistory: [],
  });

  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [newTag, setNewTag] = useState("");
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-save functionality
  useEffect(() => {
    const autoSaveTimer = setTimeout(() => {
      if (blogPost.content.trim() || blogPost.title.trim()) {
        setIsAutoSaving(true);
        setTimeout(() => {
          setIsAutoSaving(false);
          setLastSaved(new Date());
        }, 1000);
      }
    }, 3000);

    return () => clearTimeout(autoSaveTimer);
  }, [blogPost.content, blogPost.title]);

  // Update word and character count
  useEffect(() => {
    const words = blogPost.content
      .trim()
      .split(/\s+/)
      .filter(word => word.length > 0);
    setWordCount(words.length);
    setCharCount(blogPost.content.length);
  }, [blogPost.content]);

  const handleContentChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setBlogPost(prev => ({
      ...prev,
      content: e.target.value,
    }));
  }, []);

  const handleTitleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setBlogPost(prev => ({
      ...prev,
      title: e.target.value,
    }));
  }, []);

  const addTag = useCallback(() => {
    if (newTag.trim() && !blogPost.tags.includes(newTag.trim())) {
      setBlogPost(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()],
      }));
      setNewTag("");
    }
  }, [newTag, blogPost.tags]);

  const removeTag = useCallback((tagToRemove: string) => {
    setBlogPost(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  }, []);

  const insertAtCursor = useCallback(
    (text: string) => {
      const textarea = textareaRef.current;
      if (!textarea) return;

      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const content = blogPost.content;

      const newContent = content.substring(0, start) + text + content.substring(end);
      setBlogPost(prev => ({ ...prev, content: newContent }));

      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + text.length, start + text.length);
      }, 0);
    },
    [blogPost.content]
  );

  // Toolbar functions
  const insertHeading = useCallback(() => insertAtCursor("\n## Heading\n"), [insertAtCursor]);
  const insertBold = useCallback(() => insertAtCursor("**bold text**"), [insertAtCursor]);
  const insertItalic = useCallback(() => insertAtCursor("*italic text*"), [insertAtCursor]);
  const insertQuote = useCallback(() => insertAtCursor("\n> Quote text\n"), [insertAtCursor]);
  const insertHorizontalRule = useCallback(() => insertAtCursor("\n---\n"), [insertAtCursor]);
  const insertList = useCallback(
    () => insertAtCursor("\n- Item 1\n- Item 2\n- Item 3\n"),
    [insertAtCursor]
  );
  const insertLink = useCallback(
    () => insertAtCursor("[Link text](https://example.com)"),
    [insertAtCursor]
  );
  const insertImage = useCallback(() => insertAtCursor("![Alt text](image-url)"), [insertAtCursor]);
  const insertCodeBlock = useCallback(
    () => insertAtCursor('\n```javascript\n// Your code here\nconsole.log("Hello World!");\n```\n'),
    [insertAtCursor]
  );

  const insertTable = useCallback(() => {
    const tableText = `
| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |
`;
    insertAtCursor(tableText);
  }, [insertAtCursor]);

  const saveBlog = useCallback(() => {
    const newModification: ModificationEntry = {
      id: Date.now().toString(),
      timestamp: new Date(),
      description: "Blog post saved manually",
      author: "Current User",
    };

    setBlogPost(prev => ({
      ...prev,
      modificationHistory: [newModification, ...prev.modificationHistory],
    }));

    setLastSaved(new Date());
    console.log("Blog saved:", blogPost);
  }, [blogPost]);

  const exportMarkdown = useCallback(() => {
    const element = document.createElement("a");
    const file = new Blob([blogPost.content], { type: "text/markdown" });
    element.href = URL.createObjectURL(file);
    element.download = `${blogPost.title || "blog-post"}.md`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  }, [blogPost.content, blogPost.title]);

  const importMarkdown = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = e => {
        const content = e.target?.result as string;
        setBlogPost(prev => ({ ...prev, content }));
      };
      reader.readAsText(file);
    }
  }, []);

  const copyToClipboard = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(blogPost.content);
      // You could add a toast notification here
    } catch (err) {
      console.error("Failed to copy content:", err);
    }
  }, [blogPost.content]);

  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  const handleKeyboardShortcuts = useCallback(
    (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case "s":
            e.preventDefault();
            saveBlog();
            break;
          case "b":
            e.preventDefault();
            insertBold();
            break;
          case "i":
            e.preventDefault();
            insertItalic();
            break;
          case "k":
            e.preventDefault();
            insertLink();
            break;
        }
      }
    },
    [saveBlog, insertBold, insertItalic, insertLink]
  );

  useEffect(() => {
    document.addEventListener("keydown", handleKeyboardShortcuts);
    return () => document.removeEventListener("keydown", handleKeyboardShortcuts);
  }, [handleKeyboardShortcuts]);

  return (
    <PageAnimation
      type="fade-in"
      duration={0.8}
      className={`blog-editor ${isDarkMode ? "theme-dark" : ""} ${
        isFullscreen ? "fullscreen" : ""
      }`}
    >
      <motion.div
        className="blog-editor__header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <div className="blog-editor__title-section">
          <motion.input
            type="text"
            placeholder="Enter your blog title..."
            title="Enter a compelling title for your blog post. This will be used as the filename when exporting."
            value={blogPost.title}
            onChange={handleTitleChange}
            className="blog-editor__title-input"
            whileFocus={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          />
          <motion.div
            className="blog-editor__meta"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <div className="blog-editor__created-at">
              <FiClock className="icon" />
              <span>Created: {blogPost.createdAt.toLocaleDateString()}</span>
            </div>
            <div className="blog-editor__stats">
              <motion.span
                key={wordCount}
                initial={{ scale: 1.2, color: "var(--color-primary)" }}
                animate={{ scale: 1, color: "var(--color-gray-500)" }}
                transition={{ duration: 0.3 }}
              >
                {wordCount} words
              </motion.span>
              <span>•</span>
              <motion.span
                key={charCount}
                initial={{ scale: 1.2, color: "var(--color-primary)" }}
                animate={{ scale: 1, color: "var(--color-gray-500)" }}
                transition={{ duration: 0.3 }}
              >
                {charCount} characters
              </motion.span>
            </div>
            <AnimatePresence>
              {isAutoSaving && (
                <motion.div
                  className="blog-editor__auto-save"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 10 }}
                >
                  <FiRefreshCw className="icon spinning" />
                  <span>Auto-saving...</span>
                </motion.div>
              )}
              {lastSaved && !isAutoSaving && (
                <motion.div
                  className="blog-editor__last-saved"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                >
                  <FiCheck className="icon" />
                  <span>Saved {lastSaved.toLocaleTimeString()}</span>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>

        <div className="blog-editor__actions">
          {/* Primary actions - most important */}
          <motion.button
            onClick={saveBlog}
            className="blog-editor__save"
            whileHover={{
              scale: 1.05,
              boxShadow: "0 8px 25px rgba(var(--color-primary-rgb), 0.3)",
            }}
            whileTap={{ scale: 0.95 }}
          >
            <FiSave />
            <span>Save</span>
          </motion.button>

          <motion.button
            onClick={() => setIsPreviewMode(!isPreviewMode)}
            className={`blog-editor__toggle ${isPreviewMode ? "active" : ""}`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {isPreviewMode ? <FiEdit3 /> : <FiEye />}
            <span>{isPreviewMode ? "Edit" : "Preview"}</span>
          </motion.button>

          {/* Secondary actions - utility functions */}
          <motion.button
            onClick={toggleFullscreen}
            // className="blog-editor__fullscreen"
            className="blog-editor__import-btn"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            // title="Toggle fullscreen"
          >
            {isFullscreen ? <FiMinimize2 /> : <FiMaximize2 />}
            <span>Full</span>
          </motion.button>

          <motion.button
            onClick={copyToClipboard}
            // className="blog-editor__copy"
            className="blog-editor__import-btn"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            // title="Copy content to clipboard"
          >
            <FiCopy />
            <span>Copy</span>
          </motion.button>

          {/* File operations */}
          <motion.label
            className="blog-editor__import-btn"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            // title="Import markdown file"
          >
            <FiUpload />
            <span>Import</span>
            <input
              type="file"
              accept=".md,.txt"
              onChange={importMarkdown}
              style={{ display: "none" }}
            />
          </motion.label>

          <motion.button
            onClick={exportMarkdown}
            className="blog-editor__export"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            // title="Export as markdown file"
          >
            <FiDownload />
            <span>Export</span>
          </motion.button>
        </div>
      </motion.div>

      <motion.div
        className="blog-editor__tags"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <div className="blog-editor__tags-list">
          <AnimatePresence>
            {blogPost.tags.map((tag, index) => (
              <motion.span
                key={tag}
                className="blog-editor__tag"
                initial={{ opacity: 0, scale: 0.8, x: -20 }}
                animate={{ opacity: 1, scale: 1, x: 0 }}
                exit={{ opacity: 0, scale: 0.8, x: 20 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                whileHover={{ scale: 1.05, y: -2 }}
              >
                <FiTag className="icon" />
                {tag}
                <motion.button
                  onClick={() => removeTag(tag)}
                  className="blog-editor__tag-remove"
                  title={`Remove "${tag}" tag`}
                  whileHover={{ scale: 1.2, backgroundColor: "var(--color-error-500)" }}
                  whileTap={{ scale: 0.9 }}
                >
                  ×
                </motion.button>
              </motion.span>
            ))}
          </AnimatePresence>
        </div>
        <div className="blog-editor__tag-input">
          <motion.input
            type="text"
            placeholder="Add tag..."
            title="Add tags to categorize your blog post. Press Enter or click Add button to add a tag."
            value={newTag}
            onChange={e => setNewTag(e.target.value)}
            onKeyPress={e => e.key === "Enter" && addTag()}
            whileFocus={{ scale: 1.02 }}
          />
          <motion.button
            onClick={addTag}
            title="Add tag to your blog post (or press Enter)"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            disabled={!newTag.trim()}
          >
            <FiZap className="icon" />
            Add
          </motion.button>
        </div>
      </motion.div>

      <motion.div
        className="blog-editor__content"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.3 }}
      >
        <motion.div
          className="blog-editor__editor-panel"
          initial={{ opacity: 0, x: -30 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <motion.div
            className="blog-editor__toolbar"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <div className="blog-editor__toolbar-group">
              <Tooltip content="Insert Heading - Creates a markdown heading (## Heading)">
                <motion.button
                  onClick={insertHeading}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiHash />
                </motion.button>
              </Tooltip>
              <Tooltip content="Bold Text (Ctrl+B) - Makes text bold (**bold text**)">
                <motion.button
                  onClick={insertBold}
                  whileHover={{ scale: 1.1, rotate: -5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiBold />
                </motion.button>
              </Tooltip>
              <Tooltip content="Italic Text (Ctrl+I) - Makes text italic (*italic text*)">
                <motion.button
                  onClick={insertItalic}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiItalic />
                </motion.button>
              </Tooltip>
            </div>

            <div className="blog-editor__toolbar-separator"></div>

            <div className="blog-editor__toolbar-group">
              <Tooltip content="Insert Bullet List - Creates an unordered list (- Item 1)">
                <motion.button
                  onClick={insertList}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiList />
                </motion.button>
              </Tooltip>
              <Tooltip content="Insert Blockquote - Creates a quote block (> Quote text)">
                <motion.button
                  onClick={insertQuote}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiMessageSquare />
                </motion.button>
              </Tooltip>
              <Tooltip content="Insert Horizontal Rule - Creates a divider line (---)">
                <motion.button
                  onClick={insertHorizontalRule}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiMinus />
                </motion.button>
              </Tooltip>
            </div>

            <div className="blog-editor__toolbar-separator"></div>

            <div className="blog-editor__toolbar-group">
              <Tooltip content="Insert Link (Ctrl+K) - Creates a clickable link [Link text](URL)">
                <motion.button
                  onClick={insertLink}
                  whileHover={{ scale: 1.1, rotate: -5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiLink />
                </motion.button>
              </Tooltip>
              <Tooltip content="Insert Image - Embeds an image ![Alt text](image-url)">
                <motion.button
                  onClick={insertImage}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiImage />
                </motion.button>
              </Tooltip>
              <Tooltip content="Insert Table - Creates a markdown table with headers and rows">
                <motion.button
                  onClick={insertTable}
                  whileHover={{ scale: 1.1, rotate: -5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiTable />
                </motion.button>
              </Tooltip>
              <Tooltip content="Insert Code Block - Creates syntax-highlighted code (```language)">
                <motion.button
                  onClick={insertCodeBlock}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiCode />
                </motion.button>
              </Tooltip>
            </div>
          </motion.div>

          <motion.textarea
            ref={textareaRef}
            value={blogPost.content}
            onChange={handleContentChange}
            className="blog-editor__textarea"
            placeholder="Write your blog content in Markdown... Use the toolbar above for quick formatting or keyboard shortcuts: Ctrl+B (bold), Ctrl+I (italic), Ctrl+K (link), Ctrl+S (save)"
            title="Markdown Editor - Supports full markdown syntax with live preview. Use toolbar buttons or keyboard shortcuts for quick formatting."
            spellCheck="false"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            whileFocus={{
              boxShadow: "0 0 0 3px rgba(var(--color-primary-rgb), 0.1)",
              scale: 1.001,
            }}
          />
        </motion.div>

        <motion.div
          className="blog-editor__preview-panel"
          initial={{ opacity: 0, x: 30 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <motion.div
            className="blog-editor__preview-header"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <h3>Live Preview</h3>
            <motion.button
              onClick={() => setIsDarkMode(!isDarkMode)}
              className="blog-editor__theme-toggle"
              title={`Switch to ${isDarkMode ? "light" : "dark"} theme for preview and editor`}
              whileHover={{ scale: 1.1, rotate: 180 }}
              whileTap={{ scale: 0.9 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              {isDarkMode ? "☀️" : "🌙"}
            </motion.button>
          </motion.div>
          <motion.div
            className="blog-editor__preview-content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            key={blogPost.content}
          >
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
              components={{
                code(props) {
                  const { children, className } = props;
                  const match = /language-(\w+)/.exec(className || "");
                  return match ? (
                    <SyntaxHighlighter
                      style={isDarkMode ? tomorrow : prism}
                      language={match[1]}
                      PreTag="div"
                    >
                      {String(children).replace(/\n$/, "")}
                    </SyntaxHighlighter>
                  ) : (
                    <code className={className}>{children}</code>
                  );
                },
              }}
            >
              {blogPost.content}
            </ReactMarkdown>
          </motion.div>
        </motion.div>
      </motion.div>

      <motion.div
        className="blog-editor__sidebar"
        initial={{ opacity: 0, x: 30 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <div className="blog-editor__modification-history">
          <motion.h3
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            Modification History
          </motion.h3>
          <div className="blog-editor__timeline">
            <AnimatePresence>
              {blogPost.modificationHistory.length === 0 ? (
                <motion.p
                  className="blog-editor__no-history"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                >
                  No modifications yet. Save your work to start tracking changes.
                </motion.p>
              ) : (
                blogPost.modificationHistory.map((entry, index) => (
                  <motion.div
                    key={entry.id}
                    className="blog-editor__timeline-entry"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02, y: -2 }}
                  >
                    <motion.div
                      className="blog-editor__timeline-dot"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.7 + index * 0.1, type: "spring" }}
                    />
                    <div className="blog-editor__timeline-content">
                      <p className="blog-editor__timeline-description">{entry.description}</p>
                      <p className="blog-editor__timeline-meta">
                        {entry.author} • {entry.timestamp.toLocaleString()}
                      </p>
                    </div>
                  </motion.div>
                ))
              )}
            </AnimatePresence>
          </div>
        </div>
      </motion.div>
    </PageAnimation>
  );
};

export default BlogEditorPage;
