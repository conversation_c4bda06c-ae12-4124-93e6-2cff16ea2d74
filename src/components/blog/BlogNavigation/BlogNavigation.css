.blog-navigation {
  @apply py-12 border-t border-gray-200 dark:border-gray-700;
}

.blog-navigation__container {
  @apply max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 grid grid-cols-1 lg:grid-cols-3 gap-6;
}

/* Navigation Items */
.nav-item {
  @apply flex items-center justify-center;
}

.nav-item--previous {
  @apply lg:justify-start;
}

.nav-item--center {
  @apply order-first lg:order-none;
}

.nav-item--next {
  @apply lg:justify-end;
}

/* Navigation Links */
.nav-link {
  @apply flex items-center gap-4 p-6 bg-white dark:bg-gray-800 
         rounded-xl shadow-md hover:shadow-lg border border-gray-200 
         dark:border-gray-700 transition-all duration-300 
         hover:scale-105 group max-w-md w-full;
}

.nav-link--center {
  @apply flex-col gap-2 max-w-xs bg-blue-50 dark:bg-blue-900/20 
         border-blue-200 dark:border-blue-800 hover:bg-blue-100 
         dark:hover:bg-blue-900/30;
}

/* Navigation Direction */
.nav-direction {
  @apply flex items-center gap-2 text-sm font-medium text-gray-500 
         dark:text-gray-400 group-hover:text-blue-600 
         dark:group-hover:text-blue-400 transition-colors;
}

.nav-icon {
  @apply w-5 h-5;
}

/* Navigation Content */
.nav-content {
  @apply flex-1 min-w-0;
}

.nav-item--next .nav-content {
  @apply text-right;
}

.nav-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white 
         group-hover:text-blue-600 dark:group-hover:text-blue-400 
         transition-colors line-clamp-2 mb-2;
}

.nav-excerpt {
  @apply text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mb-3;
}

.nav-meta {
  @apply flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400;
}

.nav-item--next .nav-meta {
  @apply justify-end;
}

/* Placeholder */
.nav-placeholder {
  @apply flex items-center justify-center p-6 text-gray-400 
         dark:text-gray-500 text-sm italic;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .blog-navigation__container {
    @apply grid-cols-1 gap-4;
  }
  
  .nav-item--previous,
  .nav-item--next {
    @apply justify-center;
  }
  
  .nav-item--next .nav-content {
    @apply text-left;
  }
  
  .nav-item--next .nav-meta {
    @apply justify-start;
  }
  
  .nav-link {
    @apply max-w-full;
  }
}

@media (max-width: 640px) {
  .nav-link {
    @apply p-4 gap-3;
  }
  
  .nav-title {
    @apply text-base;
  }
  
  .nav-excerpt {
    @apply text-xs;
  }
}

/* Hover Effects */
.nav-link:hover .nav-icon {
  @apply transform scale-110;
}

.nav-item--previous .nav-link:hover .nav-icon {
  @apply -translate-x-1;
}

.nav-item--next .nav-link:hover .nav-icon {
  @apply translate-x-1;
}

/* Focus States */
.nav-link:focus {
  @apply ring-2 ring-blue-500 ring-offset-2 ring-offset-white 
         dark:ring-offset-gray-900;
}

/* Utility Classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animation */
.nav-link {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
  .nav-link--center {
    @apply bg-blue-900/10 border-blue-800/50 hover:bg-blue-900/20;
  }
}
