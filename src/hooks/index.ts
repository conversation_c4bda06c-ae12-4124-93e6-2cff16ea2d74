// Custom Hooks Library
export { default as useBreakpoint } from './useBreakpoint';
export { default as useHeaderScroll } from './useHeaderScroll';
export { default as useIntersectionObserver } from './useIntersectionObserver';
export { default as useLocalStorage } from './useLocalStorage';
export { default as useParallax } from './useParallax';
export { default as useScrollAnimation } from './useScrollAnimation';
export { default as useThemeSwitch } from './useThemeSwitch';

// Re-export types
export type { ThemeState } from './useThemeSwitch';